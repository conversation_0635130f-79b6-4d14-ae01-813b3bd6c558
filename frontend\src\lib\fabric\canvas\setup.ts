import { Canvas } from "fabric";
import { SetupCanvasParams } from "@/shared/types";
import { loadAnnotations } from "../operations/annotations";
import { applyCanvasFilters } from "../operations/filters";
import { loadCanvasImage } from "../rendering/image";
import { createImageLoadContainer, applyCropToCanvas } from "../operations/crop";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "../operations/transforms";

/*
Initializes a Fabric canvas for an image with optional saved annotations, crop
state, filter state, and transform state. Rebuilds background and applies
transforms/crops consistently.
*/
export const setupImageCanvas = async ({
  canvasElement,
  imageUrl,
  annotations,
  filters,
  cropData,
  existingCanvas,
  transformState,
}: SetupCanvasParams): Promise<{
  canvas: Canvas;
}> => {
  const finalImageSource = imageUrl;

  const applyTransforms = () => {
    if (transformState?.rotations) {
      for (let i = 0; i < transformState.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (transformState?.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas, transformState.rotations);
    }
    if (transformState?.flipVertical) {
      applyCanvasFlipVertical(canvas, transformState.rotations);
    }
  };
  if (existingCanvas) {
    existingCanvas.dispose();
  }

  const canvas = new Canvas(canvasElement, {
    selection: true,
    backgroundColor: "transparent",
  });

  const containerRect = canvasElement.parentElement?.getBoundingClientRect();

  if (cropData?.isCropped && cropData.normalizedCropRect) {
    const imageLoadContainer = createImageLoadContainer(cropData, containerRect);

    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: imageLoadContainer || undefined,
    });

    const transformsAtCrop = cropData.transformStateAtCrop;
    const currentTransforms = transformState;

    if (transformsAtCrop) {
      if (transformsAtCrop.rotations) {
        for (let i = 0; i < transformsAtCrop.rotations; i++) {
          applyCanvasRotation(canvas);
        }
      }
      if (transformsAtCrop.flipHorizontal) {
        applyCanvasFlipHorizontal(canvas, transformsAtCrop.rotations);
      }
      if (transformsAtCrop.flipVertical) {
        applyCanvasFlipVertical(canvas, transformsAtCrop.rotations);
      }
    }

    await applyCropToCanvas(canvas, cropData);

    if (currentTransforms && transformsAtCrop) {
      const additionalRotations =
        (currentTransforms.rotations - transformsAtCrop.rotations + 4) % 4;
      for (let i = 0; i < additionalRotations; i++) {
        applyCanvasRotation(canvas);
      }
      if (currentTransforms.flipHorizontal !== transformsAtCrop.flipHorizontal) {
        applyCanvasFlipHorizontal(canvas, currentTransforms.rotations);
      }
      if (currentTransforms.flipVertical !== transformsAtCrop.flipVertical) {
        applyCanvasFlipVertical(canvas, currentTransforms.rotations);
      }
    }
  } else {
    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: containerRect || undefined,
    });
    applyTransforms();
  }

  if (filters) {
    canvas.renderAll();
    applyCanvasFilters(canvas, filters);
  }

  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  canvas.selection = false;
  canvas.forEachObject((obj) => {
    const objName = (obj as any as Record<string, any>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  return {
    canvas,
  };
};
