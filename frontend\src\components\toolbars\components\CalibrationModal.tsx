import { Dialog, InputGroup, Button } from "@blueprintjs/core";
import { useState, FormEvent, useEffect } from "react";

interface CalibrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (value: number) => void;
}

const CalibrationModal = ({ isOpen, onClose, onSubmit }: CalibrationModalProps) => {
  const [calibrationValue, setCalibrationValue] = useState("");

  useEffect(() => {
    if (isOpen) {
      setCalibrationValue("30");
    }
  }, [isOpen]);

  const handleSubmit = () => {
    const value = parseFloat(calibrationValue);
    if (value >= 1 && value <= 500) {
      onSubmit(value);
      onClose();
    } else {
      alert("Please enter a value between 1 and 500.");
    }
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title="Enter Value"
      style={{
        width: "260px",
        position: "absolute",
        top: "50%",
        left: "55%",
        transform: "translate(-50%, -50%)",
      }}
      canEscapeKeyClose={false}
      canOutsideClickClose={false}
    >
      <div style={{ padding: "10px 20px", textAlign: "center" }}>
        <p style={{ marginBottom: "4px", fontSize: "13px" }}>
          <strong>Valid Range: 1 to 500 mm</strong>
        </p>
        <p style={{ marginBottom: "8px", fontSize: "13px" }}>Please Enter Calibration Distance:</p>
        <InputGroup
          placeholder="Enter value"
          value={calibrationValue}
          onChange={(e: FormEvent<HTMLInputElement>) => setCalibrationValue(e.currentTarget.value)}
          style={{ marginBottom: "16px", width: "100%" }}
        />
        <div style={{ display: "flex", justifyContent: "center", gap: "12px" }}>
          <Button onClick={handleSubmit} intent="primary" small>
            OK
          </Button>
          <Button onClick={onClose} small>
            Cancel
          </Button>
        </div>
      </div>
    </Dialog>
  );
};

export default CalibrationModal;
