import { Canvas, Group, Point, Rect } from "fabric";
import { CropData, SavedAnnotationsSnapshot, TransformState } from "@/shared/types";
import { syncArrowFromSaved, isArrow } from "./arrows";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "./transforms";

/*
Computes a synthetic container rect for loading the image when resuming from crop
(using saved canvasDimensions) or falls back to actual container rect.
*/
export const createImageLoadContainer = (
  cropData: CropData,
  fallbackRect?: DOMRect
): DOMRect | undefined => {
  if (!cropData.canvasDimensions) return fallbackRect;

  return {
    width: cropData.canvasDimensions.width,
    height: cropData.canvasDimensions.height,
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    bottom: cropData.canvasDimensions.height,
    right: cropData.canvasDimensions.width,
    toJSON: () => ({}),
  } as DOMRect;
};

/*
Applies the crop to the current canvas by rendering to an offscreen image, then
rebuilding the background and shifting annotations into the new coordinate space.
*/
export const applyCropToCanvas = async (canvas: Canvas, cropData: CropData): Promise<void> => {
  if (!cropData.normalizedCropRect) return;
  const cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
  if (cropRect) {
    canvas.remove(cropRect);
  }
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();
  const left = cropData.normalizedCropRect.left * canvasWidth;
  const top = cropData.normalizedCropRect.top * canvasHeight;
  const width = cropData.normalizedCropRect.width * canvasWidth;
  const height = cropData.normalizedCropRect.height * canvasHeight;

  const annotations = canvas
    .getObjects()
    .filter((obj) => (obj as any).name !== "backgroundImage" && (obj as any).name !== "cropRect");
  const previousVisibilities = annotations.map((obj) => obj.visible);
  annotations.forEach((obj) => obj.set({ visible: false }));
  canvas.renderAll();

  const fullCanvasImage = canvas.toDataURL();
  const croppedDataURL = await new Promise<string>((resolve) => {
    const img = new Image();
    img.onload = () => {
      const offscreenCanvas = document.createElement("canvas");
      offscreenCanvas.width = width;
      offscreenCanvas.height = height;
      const context2d = offscreenCanvas.getContext("2d")!;
      context2d.drawImage(img, left, top, width, height, 0, 0, width, height);
      resolve(offscreenCanvas.toDataURL());
    };
    img.src = fullCanvasImage;
  });

  annotations.forEach((obj, i) => obj.set({ visible: previousVisibilities[i] }));
  canvas.renderAll();

  const { FabricImage } = await import("fabric");
  const croppedImage = await FabricImage.fromURL(croppedDataURL, {
    crossOrigin: "anonymous",
  });

  canvas.setDimensions({ width, height });
  canvas.backgroundImage = croppedImage;
  croppedImage.set({
    left: width / 2,
    top: height / 2,
    originX: "center",
    originY: "center",
    selectable: false,
    evented: false,
    name: "backgroundImage",
  });

  canvas.forEachObject((obj) => {
    if ((obj as any).name === "backgroundImage") return;
    obj.set({
      left: (obj.left || 0) - left,
      top: (obj.top || 0) - top,
    });
  });
  canvas.renderAll();
};

/*
Returns an async function to toggle crop apply/restore.
- Apply: saves normalized crop rect, rebuilds canvas, marks crop state
- Restore: restores original image and annotations, resets crop state
*/
export const createCropHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: string,
  savedAnnotations?: SavedAnnotationsSnapshot,
  transformState?: TransformState,
  currentCropData?: CropData
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      await restoreCroppedCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        originalImageUrl || "",
        containerRef,
        savedAnnotations,
        transformState,
        currentCropData
      );
      return;
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "cropRect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    const normalizedCropRect = {
      left: left / canvasWidth,
      top: top / canvasHeight,
      width: width / canvasWidth,
      height: height / canvasHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
      transformStateAtCrop: transformState ? { ...transformState } : undefined,
    };

    await applyCropToCanvas(canvas, cropResult);

    setCropData(cropResult);
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

/*
Sets common positional/transform/styling properties onto an annotation object
from saved normalized data.
*/
const setAnnotationProps = (obj: any, item: any, width: number, height: number) => {
  const base = {
    left: (item.leftNorm ?? 0) * width,
    top: (item.topNorm ?? 0) * height,
    angle: item.angle ?? 0,
    originX: item.preserveScale ? item.originX || "center" : "center",
    originY: item.preserveScale ? item.originY || "center" : "center",
    scaleX: item.preserveScale ? item.scaleX ?? 1 : 1,
    scaleY: item.preserveScale ? item.scaleY ?? 1 : 1,
    flipX: item.flipX ?? false,
    flipY: item.flipY ?? false,
  };
  const optionalKeys = ["stroke", "strokeWidth", "fill", "name", "id"] as const;
  const extras = Object.fromEntries(
    optionalKeys.filter((k) => item[k] !== undefined).map((k) => [k, item[k]])
  );
  obj.set({ ...base, ...extras });
};

/*
Mutates an existing Fabric object to match saved annotation data for its type.
Handles preserveScale paths and arrow groups specially.
*/
const syncAnnotation = (obj: any, item: any, width: number, height: number) => {
  if (obj.type !== item.type) return;

  switch (item.type) {
    case "line": {
      const x1Abs = (item.x1Norm ?? 0) * width;
      const y1Abs = (item.y1Norm ?? 0) * height;
      const x2Abs = (item.x2Norm ?? 0) * width;
      const y2Abs = (item.y2Norm ?? 0) * height;

      const left = (item.leftNorm ?? 0) * width;
      const top = (item.topNorm ?? 0) * height;

      obj.set({
        x1: x1Abs - left,
        y1: y1Abs - top,
        x2: x2Abs - left,
        y2: y2Abs - top,
      });
      setAnnotationProps(obj, item, width, height);
      break;
    }

    case "textbox": {
      obj.set({
        text: item.text || "",
        width: (item.widthNorm ?? 0) * width,
      });
      setAnnotationProps(obj, item, width, height);
      break;
    }

    case "text": {
      const isMeasurement = item.name === "measurementText";

      obj.set({
        text: item.text || (obj as any).text || "",
        ...(item.fontSize ? { fontSize: item.fontSize } : {}),
      });

      setAnnotationProps(obj, item, width, height);

      if (isMeasurement) {
        obj.set({ scaleX: 1, scaleY: 1, originX: "center", originY: "center" });
      }

      break;
    }

    case "rect": {
      obj.set({
        width: (item.widthNorm ?? 0) * width,
        height: (item.heightNorm ?? 0) * height,
      });
      setAnnotationProps(obj, item, width, height);
      break;
    }

    case "circle": {
      const radius = Math.min((item.widthNorm ?? 0) * width, (item.heightNorm ?? 0) * height) / 2;
      obj.set({ radius });
      setAnnotationProps(obj, item, width, height);
      break;
    }

    case "path": {
      if (!item.path) break;

      if (!item.preserveScale) {
        obj.set({ path: item.path });
        setAnnotationProps(obj, item, width, height);
      } else {
        const pathWidth = (item.widthNorm ?? 0) * width;
        const pathHeight = (item.heightNorm ?? 0) * height;

        const rebuilt = item.path.map((cmd: any[]) =>
          cmd.map((val: any, idx: number) =>
            typeof val === "number" ? (idx % 2 === 1 ? val * pathWidth : val * pathHeight) : val
          )
        );

        const optionalProps: Record<string, any> = {};
        ["stroke", "strokeWidth", "fill", "angle", "flipX", "flipY", "name", "id"].forEach(
          (key) => {
            if (item[key] !== undefined) optionalProps[key] = item[key];
          }
        );

        obj.set({
          path: rebuilt,
          originX: "center",
          originY: "center",
          left: (item.leftNorm ?? 0) * width,
          top: (item.topNorm ?? 0) * height,
          scaleX: 1,
          scaleY: 1,
          ...optionalProps,
        });

        (obj as any).pathOffset = new Point(pathWidth / 2, pathHeight / 2);
        (obj as any).dirty = true;
      }
      obj.setCoords();
      break;
    }
    case "group": {
      if (
        isArrow(obj) &&
        item.name === "arrow" &&
        item.x1Norm != null &&
        item.y1Norm != null &&
        item.x2Norm != null &&
        item.y2Norm != null
      ) {
        syncArrowFromSaved(obj as Group, item, width, height);
      } else {
        setAnnotationProps(obj, item, width, height);
        obj.setCoords();
      }
      break;
    }

    default:
      break;
  }
};

const getKey = (o: any) => o?.id ?? o?.name;

const hasSavedObjects = (s: any): s is { objects: any[] } => Array.isArray(s?.objects);

/*
Iterates saved annotations and syncs any matching existing objects by id/name.
*/
const syncAnnotations = async (canvas: Canvas, saved: SavedAnnotationsSnapshot) => {
  if (!hasSavedObjects(saved)) return;

  const width = canvas.getWidth();
  const height = canvas.getHeight();

  const existing = canvas.getObjects().filter((o) => {
    const name = (o as any).name;
    return name !== "backgroundImage" && name !== "cropRect";
  });

  const byKeyExisting = new Map<string, any>();
  for (const o of existing) {
    const k = getKey(o);
    if (k) byKeyExisting.set(k, o);
  }

  for (const item of saved.objects) {
    const key = getKey(item);
    if (!key) continue;
    const obj = byKeyExisting.get(key);
    if (!obj) continue;
    if (obj.type !== item.type) continue;

    syncAnnotation(obj, item, width, height);
    obj.setCoords();
  }

  canvas.requestRenderAll();
};

/*
Rebuilds the canvas back to original image, reapplies transforms (rotations/flips)
up to crop time and then additional deltas. Restores annotations when available.
*/
export const restoreCroppedCanvas = async (
  canvas: Canvas,
  setCropData: (data: CropData) => void,
  setHasPerformedCrop: (value: boolean) => void,
  originalImageUrl: string,
  containerRef?: React.RefObject<HTMLElement | null>,
  savedAnnotations?: any,
  transformState?: TransformState,
  currentCropData?: CropData
) => {
  if (!canvas || !originalImageUrl) return;

  const containerRect = containerRef?.current?.getBoundingClientRect();
  if (!containerRect) return;

  const { FabricImage } = await import("fabric");
  const originalImage = await FabricImage.fromURL(originalImageUrl, {
    crossOrigin: "anonymous",
  });

  const imageAspect = (originalImage.width || 1) / (originalImage.height || 1);
  const containerAspect = containerRect.width / containerRect.height;

  let scale: number;
  let targetWidth: number;
  let targetHeight: number;

  if (imageAspect > containerAspect) {
    targetWidth = containerRect.width;
    targetHeight = containerRect.width / imageAspect;
    scale = targetWidth / (originalImage.width || 1);
  } else {
    targetHeight = containerRect.height;
    targetWidth = targetHeight * imageAspect;
    scale = targetHeight / (originalImage.height || 1);
  }

  canvas.setDimensions({ width: targetWidth, height: targetHeight });

  originalImage.set({
    left: targetWidth / 2,
    top: targetHeight / 2,
    originX: "center",
    originY: "center",
    scaleX: scale,
    scaleY: scale,
    selectable: false,
    evented: false,
    name: "backgroundImage",
  });
  canvas.backgroundImage = originalImage;

  const snapshotTransform = savedAnnotations?.transformStateAtSnapshot;
  const fallbackTransform = currentCropData?.transformStateAtCrop;
  const baseTransform = snapshotTransform || fallbackTransform;

  if (baseTransform) {
    if (baseTransform.rotations) {
      for (let i = 0; i < baseTransform.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (baseTransform.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas, baseTransform.rotations);
    }
    if (baseTransform.flipVertical) {
      applyCanvasFlipVertical(canvas, baseTransform.rotations);
    }
  }

  if (hasSavedObjects(savedAnnotations)) {
    await syncAnnotations(canvas, savedAnnotations);
  }

  if (transformState && baseTransform) {
    const fromRot = baseTransform.rotations || 0;
    const toRot = transformState.rotations || 0;
    const rotationDelta = (toRot - fromRot + 4) % 4;
    for (let i = 0; i < rotationDelta; i++) {
      applyCanvasRotation(canvas);
    }

    const flipXDelta = transformState.flipHorizontal !== baseTransform.flipHorizontal;
    const flipYDelta = transformState.flipVertical !== baseTransform.flipVertical;

    if (flipXDelta) {
      applyCanvasFlipHorizontal(canvas, transformState.rotations);
    }
    if (flipYDelta) {
      applyCanvasFlipVertical(canvas, transformState.rotations);
    }
  }

  setCropData({
    isCropped: false,
    normalizedCropRect: undefined,
    canvasDimensions: undefined,
  });
  setHasPerformedCrop(false);

  canvas.renderAll();
};
