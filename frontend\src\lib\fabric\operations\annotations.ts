import { Canvas, FabricImage } from "fabric";
import { scaleCanvasObjects } from "../rendering/resize";

export const loadAnnotations = async (canvas: Canvas, annotations: any): Promise<void> => {
  if (!annotations || !canvas) return;
  const backgroundImage = canvas.backgroundImage as FabricImage | null;
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const annotationsData = annotations as {
    canvasWidth?: number;
    canvasHeight?: number;
    objects?: any[];
    [key: string]: any;
  };

  await canvas.loadFromJSON(annotations);

  if (backgroundImage) {
    canvas.backgroundImage = backgroundImage;
  }

  if (annotationsData.canvasWidth && annotationsData.canvasHeight) {
    const savedWidth = annotationsData.canvasWidth;
    const savedHeight = annotationsData.canvasHeight;

    const scaleX = currentWidth / savedWidth;
    const scaleY = currentHeight / savedHeight;
    const imageScale = Math.min(scaleX, scaleY);
    scaleCanvasObjects(canvas, imageScale);
  }

  const lines = canvas.getObjects().filter((obj: any) => obj.name === "measurementLine");
  const texts = canvas.getObjects().filter((obj: any) => obj.name === "measurementText");

  lines.forEach((line: any) => {
    const x1 = line.left + line.x1 * line.scaleX;
    const y1 = line.top + line.y1 * line.scaleY;
    const x2 = line.left + line.x2 * line.scaleX;
    const y2 = line.top + line.y2 * line.scaleY;
    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;
    const matchingText = texts.find((text: any) => {
      const dx = Math.abs((text.left ?? 0) - midX);
      const dy = Math.abs((text.top ?? 0) - midY);
      return dx < 20 && dy < 20;
    });

    if (matchingText) {
      line.measurementText = matchingText;
    }
  });

  canvas.forEachObject((obj) => {
    const objName = (obj as any as Record<string, any>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  canvas.calcOffset();
  canvas.renderAll();
};
