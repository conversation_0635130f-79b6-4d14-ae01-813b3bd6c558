import { Canvas, Group, Line, Triangle } from "fabric";
import { v4 as uuidv4 } from "uuid";

/*
Creates an arrow annotation as a Fabric Group composed of:
- Line (shaft) starting at [0,0] and extending toward drag direction
- Triangle (head) positioned/rotated based on drag angle
The group is positioned at the initial pointer (start), and children remain non-interactive.
*/
export const createArrow = (
  start: { x: number; y: number },
  config: any
): Group & { id: string } => {
  const size = Math.max(6, Number(config.arrowHeadSize ?? 14));

  const shaft = new Line([0, 0, 0, 0], {
    stroke: config.stroke,
    strokeWidth: config.strokeWidth,
    strokeUniform: true,
    selectable: false,
    evented: false,
  });

  const head = new Triangle({
    width: size,
    height: size,
    fill: config.stroke,
    originX: "center",
    originY: "center",
    left: 0,
    top: 0,
    selectable: false,
    evented: false,
  });

  const group = new Group([shaft, head], {
    left: start.x,
    top: start.y,
    originX: "center",
    originY: "center",
    objectCaching: false,
    selectable: false,
    evented: false,
    name: "arrow",
  } as any) as Group & { id: string; name?: string };

  group.id = uuidv4();
  return group;
};

export const updateArrowOnModify = (canvas: Canvas, arrow: Group): void => {
  const [shaft, head] = (arrow as any)._objects as [Line, Triangle];
  if (!shaft || !head) return;

  const headSize = (head as any).height ?? 0;

  head.set({
    left: Math.cos(arrow.angle || 0) * (headSize / 2),
    top: Math.sin(arrow.angle || 0) * (headSize / 2),
  });

  canvas.renderAll();
};

/*
Updates arrow geometry during drag:
- Computes angle from startPoint to currentPoint
- Repositions/rotates head; adjusts shaft endpoints to avoid head overlap
*/
export const updateArrowSize = (
  group: Group,
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number }
): void => {
  const [shaft, head] = (group as any)._objects as [Line, Triangle];

  const dx = currentPoint.x - startPoint.x;
  const dy = currentPoint.y - startPoint.y;

  const angleRad = Math.atan2(dy, dx);
  const angleDeg = (angleRad * 180) / Math.PI;

  const headSize = (head as any).height ?? 0;
  head.set({
    left: Math.cos(angleRad) * (headSize / 2),
    top: Math.sin(angleRad) * (headSize / 2),
    angle: angleDeg - 90,
  });

  shaft.set({
    x1: Math.cos(angleRad) * headSize,
    y1: Math.sin(angleRad) * headSize,
    x2: dx,
    y2: dy,
  });

  group.set("dirty", true);
  group.setCoords();
  group.canvas?.requestRenderAll();
};

export const syncArrowFromSaved = (
  group: Group,
  item: any,
  width: number,
  height: number
): void => {
  const [shaft, head] = (group as any)._objects as [Line, Triangle];

  const x1 = item.x1Norm * width;
  const y1 = item.y1Norm * height;
  const x2 = item.x2Norm * width;
  const y2 = item.y2Norm * height;

  const dx = x2 - x1;
  const dy = y2 - y1;

  const ang = Math.atan2(dy, dx);
  const angDeg = (ang * 180) / Math.PI;
  const headSize = head?.height ?? 0;

  group.set({
    left: x1,
    top: y1,
    originX: "center",
    originY: "center",
    scaleX: 1,
    scaleY: 1,
  });

  head?.set({
    left: Math.cos(ang) * (headSize / 2),
    top: Math.sin(ang) * (headSize / 2),
    angle: angDeg - 90,
  });

  shaft?.set({
    x1: Math.cos(ang) * headSize,
    y1: Math.sin(ang) * headSize,
    x2: dx,
    y2: dy,
  });

  if (item.id !== undefined) (group as any).id = item.id;
  (group as any).dirty = true;
  group.setCoords();
};

export const isArrow = (obj: any): obj is Group => {
  return (obj as any)?.name === "arrow" && (obj as any)?.type === "group";
};
