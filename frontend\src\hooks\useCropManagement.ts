import { useState } from "react";
import { Canvas, Group, Line, Path, Textbox } from "fabric";
import {
  CropData,
  CropManagementState,
  UndoTrackingState,
  TransformState,
  SavedAnnotationsSnapshot,
} from "@/shared/types";
import { create<PERSON><PERSON><PERSON>and<PERSON> } from "@/lib/fabric/operations";

/*
Manages crop state and snapshot/restore of annotations across crop operations.
Persists normalized annotation data while cropped.
*/
export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData,
  undoTracking: UndoTrackingState,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: string,
  transformState?: TransformState
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);
  const [hasPerformedCrop, setHasPerformedCrop] = useState(initialCropData.isCropped || false);
  const [savedAnnotations, setSavedAnnotations] = useState<SavedAnnotationsSnapshot | undefined>(
    undefined
  );

  // Excludes background and crop rect from annotation persistence
  const filterDrawableObjects = (canvas: Canvas) =>
    canvas
      .getObjects()
      .filter((obj) => (obj as any).name !== "backgroundImage" && (obj as any).name !== "cropRect");

  const getBaseProps = (obj: any, width: number, height: number) => {
    const center = obj.getCenterPoint();
    return {
      type: obj.type,
      name: obj.name,
      angle: obj.angle || 0,
      fill: obj.fill,
      stroke: obj.stroke,
      strokeWidth: obj.strokeWidth,
      scaleX: obj.scaleX,
      scaleY: obj.scaleY,
      originX: obj.originX,
      originY: obj.originY,
      flipX: obj.flipX || false,
      flipY: obj.flipY || false,
      preserveScale: hasPerformedCrop,
      id: obj.id,
      leftNorm: center.x / width,
      topNorm: center.y / height,
    };
  };

  const getNormalizedAnnotations = (
    canvas: Canvas,
    originalWidth: number,
    originalHeight: number,
    crop?: CropData
  ) => {
    const { left = 0, top = 0, width = 1, height = 1 } = crop?.normalizedCropRect || {};
    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    const scaleX = width * originalWidth;
    const scaleY = height * originalHeight;

    const objs = filterDrawableObjects(canvas).map((obj: any) => {
      const base: any = {
        type: obj.type,
        name: obj.name,
        angle: obj.angle || 0,
        fill: obj.fill,
        stroke: obj.stroke,
        strokeWidth: obj.strokeWidth,
        scaleX: obj.scaleX,
        scaleY: obj.scaleY,
        originX: obj.originX,
        originY: obj.originY,
        flipX: obj.flipX || false,
        flipY: obj.flipY || false,
        preserveScale: hasPerformedCrop,
        id: obj.id,
      };

      const fullLeft = left * originalWidth + (obj.left! / canvasWidth) * scaleX;
      const fullTop = top * originalHeight + (obj.top! / canvasHeight) * scaleY;

      base.leftNorm = fullLeft / originalWidth;
      base.topNorm = fullTop / originalHeight;

      if (obj.type === "line") {
        const line = obj as Line;
        const getCoord = (val: number, axis: "x" | "y") =>
          (axis === "x" ? left * originalWidth : top * originalHeight) +
          (val / (axis === "x" ? canvasWidth : canvasHeight)) * (axis === "x" ? scaleX : scaleY);

        return {
          ...base,
          x1Norm: getCoord(line.x1!, "x") / originalWidth,
          y1Norm: getCoord(line.y1!, "y") / originalHeight,
          x2Norm: getCoord(line.x2!, "x") / originalWidth,
          y2Norm: getCoord(line.y2!, "y") / originalHeight,
        };
      }

      if (obj.type === "path") {
        const path = obj as Path;

        const bounds = path.getBoundingRect();
        const bw = bounds.width || 1;
        const bh = bounds.height || 1;

        const normPath = path.path?.map((cmd) =>
          cmd.map((val, idx) => {
            if (typeof val !== "number") return val;
            const isX = idx % 2 === 1;
            return isX ? (val - bounds.left) / bw : (val - bounds.top) / bh;
          })
        );

        const center = path.getCenterPoint();

        const leftFull = left * originalWidth + (center.x / canvasWidth) * (width * originalWidth);
        const topFull =
          top * originalHeight + (center.y / canvasHeight) * (height * originalHeight);

        return {
          type: "path",
          name: (path as any).name,
          id: (path as any).id,
          angle: path.angle || 0,
          fill: path.fill,
          stroke: path.stroke,
          strokeWidth: path.strokeWidth,
          originX: "center",
          originY: "center",
          flipX: path.flipX || false,
          flipY: path.flipY || false,
          preserveScale: hasPerformedCrop,

          leftNorm: leftFull / originalWidth,
          topNorm: topFull / originalHeight,

          widthNorm: (bw / canvasWidth) * width,
          heightNorm: (bh / canvasHeight) * height,

          path: normPath,
        };
      }

      if (obj.type === "group" && (obj as any).name === "arrow") {
        const group = obj as Group;
        const [shaft] = (group as any)._objects as [fabric.Line, fabric.Triangle];

        const mapX = (x: number) =>
          left * originalWidth + (x / canvasWidth) * (width * originalWidth);
        const mapY = (y: number) =>
          top * originalHeight + (y / canvasHeight) * (height * originalHeight);

        const x1Full = mapX(group.left!);
        const y1Full = mapY(group.top!);
        const x2Full = mapX(group.left! + (shaft?.x2 ?? 0));
        const y2Full = mapY(group.top! + (shaft?.y2 ?? 0));

        return {
          type: "group",
          name: (group as any).name,
          id: (group as any).id,
          preserveScale: hasPerformedCrop,
          originX: "center",
          originY: "center",
          x1Norm: x1Full / originalWidth,
          y1Norm: y1Full / originalHeight,
          x2Norm: x2Full / originalWidth,
          y2Norm: y2Full / originalHeight,
        };
      }

      if (obj.type === "text" && (obj as any).name === "measurementText") {
        const c = obj.getCenterPoint();

        const mapX = (x: number) =>
          left * originalWidth + (x / canvasWidth) * (width * originalWidth);
        const mapY = (y: number) =>
          top * originalHeight + (y / canvasHeight) * (height * originalHeight);

        const leftFull = mapX(c.x);
        const topFull = mapY(c.y);

        return {
          type: "text",
          name: (obj as any).name,
          id: (obj as any).id,
          preserveScale: hasPerformedCrop,
          originX: "center",
          originY: "center",
          leftNorm: leftFull / originalWidth,
          topNorm: topFull / originalHeight,
          text: (obj as any).text,
          fontSize: (obj as any).fontSize,
          stroke: obj.stroke,
          strokeWidth: obj.strokeWidth,
          fill: obj.fill,
        };
      }

      const rawWidth = (obj.width ?? 0) * (obj.scaleX ?? 1);
      const rawHeight = (obj.height ?? 0) * (obj.scaleY ?? 1);

      return {
        ...base,
        widthNorm: (rawWidth / canvasWidth) * width,
        heightNorm: (rawHeight / canvasHeight) * height,
        ...((obj as any).text && { text: (obj as Textbox).text }),
      };
    });

    return {
      objects: objs,
      canvasWidth: originalWidth,
      canvasHeight: originalHeight,
    };
  };

  const getNormalizedAnnotationsForUncropped = (canvas: Canvas) => {
    const width = canvas.getWidth();
    const height = canvas.getHeight();

    const objects = filterDrawableObjects(canvas).map((obj) => {
      const base = getBaseProps(obj, width, height);

      if (obj.type === "line") {
        const line = obj as Line;
        return {
          ...base,
          x1Norm: line.x1! / width,
          y1Norm: line.y1! / height,
          x2Norm: line.x2! / width,
          y2Norm: line.y2! / height,
        };
      }

      if (obj.type === "path") {
        return {
          ...base,
          path: (obj as Path).path,
        };
      }

      return {
        ...base,
        widthNorm: obj.getScaledWidth() / width,
        heightNorm: obj.getScaledHeight() / height,
        ...((obj as any).text && { text: (obj as Textbox).text }),
      };
    });

    return { objects, canvasWidth: width, canvasHeight: height };
  };

  const handleCrop = async () => {
    if (!hasPerformedCrop && fabricCanvas.current) {
      const canvas = fabricCanvas.current;
      const visibleObjs = filterDrawableObjects(canvas);

      if (visibleObjs.length) {
        const uncropped = getNormalizedAnnotationsForUncropped(canvas);
        const bg = canvas.backgroundImage as any;
        const bgTransformAtSnapshot = bg
          ? {
              angle: bg.angle || 0,
              flipX: !!bg.flipX,
              flipY: !!bg.flipY,
            }
          : undefined;
        const snapshot: SavedAnnotationsSnapshot = {
          ...uncropped,
          transformStateAtSnapshot: transformState ? { ...transformState } : undefined,
          bgTransformAtSnapshot,
        };
        setSavedAnnotations(snapshot);
      }
    }

    const cropOperation = createCropHandler(
      fabricCanvas,
      hasPerformedCrop,
      setCropData,
      undoTracking.isUndoingRef,
      setHasPerformedCrop,
      containerRef,
      originalImageUrl,
      savedAnnotations,
      transformState,
      cropData
    );

    await cropOperation();

    if (hasPerformedCrop) setSavedAnnotations(undefined);
  };

  const handleShapeCreated = () => {
    if (!fabricCanvas.current || !hasPerformedCrop) return;

    const canvas = fabricCanvas.current;
    const originalCanvasWidth = cropData.canvasDimensions?.width || canvas.getWidth();
    const originalCanvasHeight = cropData.canvasDimensions?.height || canvas.getHeight();

    const snapshot = getNormalizedAnnotations(
      canvas,
      originalCanvasWidth,
      originalCanvasHeight,
      cropData
    );

    const current = snapshot.objects;
    const existing = savedAnnotations?.objects || [];
    const existingIds = new Set(existing.map((obj: any) => obj.id));
    const filtered = current.filter((obj: any) => !existingIds.has(obj.id));

    const merged: SavedAnnotationsSnapshot = {
      ...snapshot,
      objects: [...existing, ...filtered],
      transformStateAtSnapshot: transformState ? { ...transformState } : undefined,
      bgTransformAtSnapshot: ((): any => {
        const bg = canvas.backgroundImage as any;
        return bg
          ? {
              angle: bg.angle || 0,
              flipX: !!bg.flipX,
              flipY: !!bg.flipY,
            }
          : savedAnnotations?.bgTransformAtSnapshot;
      })(),
    };

    setSavedAnnotations(merged);
  };

  return {
    cropData,
    setCropData,
    hasPerformedCrop,
    setHasPerformedCrop,
    handleCrop,
    handleShapeCreated,
  };
};
