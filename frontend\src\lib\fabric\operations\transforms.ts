import { Canvas, Point } from "fabric";
import { TransformState } from "@/shared/types";

/*
Rotates the canvas 90deg clockwise: swaps canvas dimensions, rotates background, and
repositions/rotates all objects around canvas center.
*/
export const applyCanvasRotation = (canvas: Canvas, onRotationComplete?: () => void): void => {
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  canvas.setDimensions({
    width: currentHeight,
    height: currentWidth,
  });

  if (canvas.backgroundImage) {
    const bgImage = canvas.backgroundImage;
    const currentAngle = bgImage.angle || 0;
    const newAngle = (currentAngle + 90) % 360;

    bgImage.set({
      angle: newAngle,
      originX: "center",
      originY: "center",
      left: canvas.width! / 2,
      top: canvas.height! / 2,
    });
  }

  const oldCenter = new Point(currentWidth / 2, currentHeight / 2);
  const newCenter = new Point(currentHeight / 2, currentWidth / 2);

  canvas.getObjects().forEach((obj) => {
    const objCenter = obj.getCenterPoint();

    const dx = objCenter.x - oldCenter.x;
    const dy = objCenter.y - oldCenter.y;

    const newX = newCenter.x - dy;
    const newY = newCenter.y + dx;

    obj.set({
      left: newX,
      top: newY,
      angle: (obj.angle || 0) + 90,
      originX: "center",
      originY: "center",
    });

    obj.setCoords();
  });

  canvas.requestRenderAll();
  if (onRotationComplete) {
    onRotationComplete();
  }
};

const applyCanvasFlip = (
  canvas: Canvas,
  direction: "horizontal" | "vertical",
  rotations: number = 0
): void => {
  const center = new Point(canvas.getWidth() / 2, canvas.getHeight() / 2);
  const currentRotation = canvas.backgroundImage?.angle || 0;
  const currentRotations = Math.round(currentRotation / 90) % 4;
  const effectiveRotations = rotations > 0 ? rotations % 4 : currentRotations;
  const isHorizontal = direction === "horizontal";

  if (canvas.backgroundImage) {
    if (effectiveRotations % 2 === 0) {
      const bgFlipProp = isHorizontal ? "flipX" : "flipY";
      canvas.backgroundImage.set(bgFlipProp, !canvas.backgroundImage[bgFlipProp]);
    } else {
      const bgFlipProp = isHorizontal ? "flipY" : "flipX";
      canvas.backgroundImage.set(bgFlipProp, !canvas.backgroundImage[bgFlipProp]);
    }
  }

  canvas.getObjects().forEach((obj) => {
    const objCenter = obj.getCenterPoint();
    const dx = objCenter.x - center.x;
    const dy = objCenter.y - center.y;
    const objAngle = obj.angle || 0;
    const normalizedAngle = ((objAngle % 360) + 360) % 360;
    const objIsRotated = normalizedAngle === 90 || normalizedAngle === 270;

    let newX, newY;
    let flipProp: "flipX" | "flipY";

    if (!objIsRotated) {
      if (isHorizontal) {
        newX = center.x - dx;
        newY = center.y + dy;
        flipProp = "flipX";
      } else {
        newX = center.x + dx;
        newY = center.y - dy;
        flipProp = "flipY";
      }
    } else {
      if (isHorizontal) {
        newX = center.x - dx;
        newY = center.y + dy;
        flipProp = "flipY";
      } else {
        newX = center.x + dx;
        newY = center.y - dy;
        flipProp = "flipX";
      }
    }

    const originalFlipX = obj.flipX;
    const originalFlipY = obj.flipY;

    obj.set({
      left: newX,
      top: newY,
      [flipProp]: !obj[flipProp],
      originX: "center",
      originY: "center",
    });

    if (obj.type === "text" || (obj as any).name === "measurementText" || obj.type === "textbox") {
      obj.set({
        flipX: originalFlipX,
        flipY: originalFlipY,
      });
    }

    obj.setCoords();
  });

  canvas.requestRenderAll();
};

/*
Flips canvas content horizontally relative to current or provided rotation state.
*/
export const applyCanvasFlipHorizontal = (canvas: Canvas, rotations: number = 0): void => {
  applyCanvasFlip(canvas, "horizontal", rotations);
};

/*
Flips canvas content vertically relative to current or provided rotation state.
*/
export const applyCanvasFlipVertical = (canvas: Canvas, rotations: number = 0): void => {
  applyCanvasFlip(canvas, "vertical", rotations);
};

export const createRotateHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  onRotationComplete?: () => void
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasRotation(fabricCanvas.current, onRotationComplete);
      setTransformState((prev) => ({ ...prev, rotations: (prev.rotations + 1) % 4 }));
    }
  };
};

export const createFlipHorizontalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  getCurrentTransformState?: () => TransformState
) => {
  return () => {
    if (fabricCanvas?.current) {
      const currentState = getCurrentTransformState?.() || {
        rotations: 0,
        flipHorizontal: false,
        flipVertical: false,
      };
      applyCanvasFlipHorizontal(fabricCanvas.current, currentState.rotations);
      setTransformState((prev) => ({ ...prev, flipHorizontal: !prev.flipHorizontal }));
    }
  };
};

export const createFlipVerticalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  getCurrentTransformState?: () => TransformState
) => {
  return () => {
    if (fabricCanvas?.current) {
      const currentState = getCurrentTransformState?.() || {
        rotations: 0,
        flipHorizontal: false,
        flipVertical: false,
      };
      applyCanvasFlipVertical(fabricCanvas.current, currentState.rotations);
      setTransformState((prev) => ({ ...prev, flipVertical: !prev.flipVertical }));
    }
  };
};
