const { StackConfig, VolumeConfig, ImageConfig } = require("../models/ViewerConfig");
const { serverConfig } = require("../config/appConfig");

const generateFilePath = (id, type) => {
  if (type === "stack") {
    return `/files/${id.toUpperCase()}.dcm`;
  } else if (type === "volume") {
    return `/files/${id}/`;
  } else if (type === "image") {
    return `/files/${id}.jpg`;
  }
  return null;
};
const createDefaultConfig = (fileInfo) => {
  if (!fileInfo) return null;

  const { baseUrl } = serverConfig;
  const { id, type } = fileInfo;

  const filePath = generateFilePath(id, type);
  if (!filePath) {
    throw new Error(`Cannot generate path for file type: ${type}`);
  }

  if (type === "stack") {
    return {
      id,
      viewer: {
        imageUrl: `wadouri:${baseUrl}${filePath}`,
      },
    };
  } else if (type === "volume") {
    const volumeUrls = Array(291)
      .fill(0)
      .map((_, i) => {
        const paddedIndex = 1 + i;
        return `wadouri:${baseUrl}${filePath}3DSlice${paddedIndex}.dcm`;
      });

    return {
      id,
      viewer: {
        imageUrl: volumeUrls,
      },
    };
  } else if (type === "image") {
    return {
      id,
      viewer: {
        imageUrl: `${baseUrl}${filePath}`,
      },
    };
  }

  return null;
};

const createDataGetter = (ConfigModel, dataType) => {
  return async (id, fileInfo) => {
    try {
      let config = await ConfigModel.findOne({ id });
      if (!config) {
        const defaultConfig = createDefaultConfig(fileInfo);
        if (defaultConfig) {
          config = new ConfigModel(defaultConfig);
          await config.save();
        } else {
          throw new Error(`Cannot create config for file ID: ${id}`);
        }
      }
      return config.toObject();
    } catch (error) {
      console.error(`Error fetching ${dataType} data from MongoDB:`, error.message);
      throw error;
    }
  };
};

const createConfigUpdater = (ConfigModel, dataType, useFieldMapping = true) => {
  return async (id, configUpdates) => {
    try {
      let updateQuery;

      if (useFieldMapping) {
        const setFields = {};
        Object.keys(configUpdates).forEach((key) => {
          if (configUpdates[key] !== undefined) {
            setFields[`viewer.configs.${key}`] = configUpdates[key];
          }
        });
        updateQuery = { $set: setFields };
      } else {
        updateQuery = { $set: { "viewer.configs": configUpdates } };
      }

      const updatedConfig = await ConfigModel.findOneAndUpdate({ id }, updateQuery, {
        new: true,
        upsert: true,
      });
      return updatedConfig.viewer.configs;
    } catch (error) {
      console.error(`Error updating ${dataType} config in MongoDB:`, error.message);
      throw error;
    }
  };
};

const getStackData = createDataGetter(StackConfig, "stack");
const getVolumeData = createDataGetter(VolumeConfig, "volume");
const getImageData = createDataGetter(ImageConfig, "image");

// Volume-related config updates (Cornerstone)
const updateVolumeConfig = createConfigUpdater(VolumeConfig, "volume", true);

const updateFabricConfig = async (id, fabricConfigUpdates) => {
  try {
    const setFields = {};
    Object.keys(fabricConfigUpdates).forEach((key) => {
      if (fabricConfigUpdates[key] !== undefined) {
        setFields[`viewer.fabricConfigs.${key}`] = fabricConfigUpdates[key];
      }
    });

    const updateQuery = { $set: setFields };

    // Try to update in StackConfig first, then ImageConfig if not found
    let updatedConfig = await StackConfig.findOneAndUpdate({ id }, updateQuery, {
      new: true,
      upsert: false,
    });

    if (!updatedConfig) {
      updatedConfig = await ImageConfig.findOneAndUpdate({ id }, updateQuery, {
        new: true,
        upsert: false,
      });
    }

    if (!updatedConfig) {
      throw new Error(`Config with ID ${id} not found`);
    }

    return updatedConfig.viewer.fabricConfigs;
  } catch (error) {
    console.error("Error updating fabric config in MongoDB:", error.message);
    throw error;
  }
};

const resetFabricConfig = async (id) => {
  try {
    // Import centralized default values
    const { DEFAULT_FABRIC_CONFIGS } = require("../config/defaultFabricConfigs");
    const defaultFabricConfigs = DEFAULT_FABRIC_CONFIGS;

    const updateQuery = { $set: { "viewer.fabricConfigs": defaultFabricConfigs } };

    // Try to update in StackConfig first, then ImageConfig if not found
    let updatedConfig = await StackConfig.findOneAndUpdate({ id }, updateQuery, {
      new: true,
      upsert: false,
    });

    if (!updatedConfig) {
      updatedConfig = await ImageConfig.findOneAndUpdate({ id }, updateQuery, {
        new: true,
        upsert: false,
      });
    }

    if (!updatedConfig) {
      throw new Error(`Config with ID ${id} not found`);
    }

    return updatedConfig;
  } catch (error) {
    console.error("Error resetting fabric config:", error);
    throw error;
  }
};

module.exports = {
  getStackData,
  getVolumeData,
  getImageData,
  updateVolumeConfig,
  updateFabricConfig,
  resetFabricConfig,
};
