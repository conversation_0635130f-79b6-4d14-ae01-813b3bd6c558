import React, { useState, useEffect } from "react";
import { ImageToolbarProps, CalibrationData } from "@/shared/types";
import { useFabricTools } from "@/hooks/useFabricTools";
import {
  ToolGrid,
  TransformControls,
  SliderControls,
  GammaControls,
  ActionButtons,
  CalibrationPrompt,
  ColorControls,
} from "./components";
import CalibrationModal from "./components/CalibrationModal";
import { Rnd } from "react-rnd";
import {
  createCalibrationSubmitHandler,
  createCalibrationCloseHandler,
  getCalibrationFromLocalStorage,
  clearCalibrationFromLocalStorage,
} from "@/lib/fabric/operations";
import { FaGripVertical, FaArrowsAlt } from "react-icons/fa";

const FabricToolbar: React.FC<ImageToolbarProps> = ({
  fabricCanvas,
  fabricConfigs,
  handlers,
  state,
  config = {},
  onShapeCreated,
}) => {
  const { disableGrayscale = false, disableGamma = false } = config;
  const [isCalibrationModalOpen, setCalibrationModalOpen] = useState(false);
  const [isCalibrationPromptOpen, setCalibrationPromptOpen] = useState(false);
  const [localCalibrationData, setLocalCalibrationData] = useState<CalibrationData>(() => {
    const localStorageData = getCalibrationFromLocalStorage();
    return localStorageData ?? fabricConfigs.calibrationData!;
  });
  useEffect(() => {
    if (fabricConfigs.calibrationData) {
      setLocalCalibrationData(fabricConfigs.calibrationData);
    }
    return () => {
      clearCalibrationFromLocalStorage();
    };
  }, [fabricConfigs.calibrationData]);

  const { activeMode, changeToolMode } = useFabricTools({
    fabricCanvas,
    hasPerformedCrop: state.hasPerformedCrop,
    onShapeCreated,
    onCrop: handlers.actions.handleCrop,
    disableUndoTracking: handlers.tracking.disableUndoTracking,
    enableUndoTracking: handlers.tracking.enableUndoTracking,
    showCalibrationModal: () => setCalibrationModalOpen(true),
    calibrationData: localCalibrationData || undefined,
    onCalibrationPrompt: () => setCalibrationPromptOpen(true),
  });

  const [annotationColor, setAnnotationColor] = useState<string>(
    fabricConfigs.annotationColor || "#ff0000"
  );

  // Handles the change in annotation color from the UI
  useEffect(() => {
    const canvas = fabricCanvas?.current;
    if (!canvas) return;
    (canvas as any).annotationColor = annotationColor;
  }, [annotationColor, fabricCanvas]);
  return (
    <>
      <Rnd
        default={{
          x: 0,
          y: window.innerHeight / 2 - 150,
          width: 175,
          height: 340,
        }}
        bounds=".viewer-panel"
        enableResizing={false}
        dragHandleClassName="drag-handle"
      >
        <div className="fabric-toolbar-vertical">
          <div className="drag-handle">
            <FaGripVertical className="drag-icon" />
            <span className="drag-text">Tools</span>
            <FaArrowsAlt className="drag-icon" />
          </div>
          <div className="annotation-tools">
            <ToolGrid
              activeMode={activeMode}
              hasPerformedCrop={state.hasPerformedCrop}
              onToolSelect={changeToolMode}
              onCrop={handlers.actions.handleCrop}
            />
            <TransformControls
              grayscale={fabricConfigs.grayscale}
              invert={fabricConfigs.invert}
              disableGrayscale={disableGrayscale}
              onRotate={handlers.transform.handleRotate}
              onFlipHorizontal={handlers.transform.handleFlipHorizontal}
              onFlipVertical={handlers.transform.handleFlipVertical}
              onGrayscaleChange={handlers.filter.handleGrayscaleChange}
              onInvertChange={handlers.filter.handleInvertChange}
            />
            <ColorControls color={annotationColor} onChange={setAnnotationColor} />
          </div>
          <SliderControls
            brightness={fabricConfigs.brightness}
            contrast={fabricConfigs.contrast}
            sharpness={fabricConfigs.sharpness}
            onBrightnessChange={handlers.filter.handleBrightnessChange}
            onContrastChange={handlers.filter.handleContrastChange}
            onSharpnessChange={handlers.filter.handleSharpnessChange}
          />

          <GammaControls
            gammaR={fabricConfigs.gammaR}
            gammaG={fabricConfigs.gammaG}
            gammaB={fabricConfigs.gammaB}
            disableGamma={disableGamma}
            onGammaRChange={handlers.filter.handleGammaRChange}
            onGammaGChange={handlers.filter.handleGammaGChange}
            onGammaBChange={handlers.filter.handleGammaBChange}
          />
          <ActionButtons
            canUndo={state.canUndo}
            onUndo={handlers.actions.handleUndo}
            onSave={handlers.actions.handleSave}
          />
        </div>
      </Rnd>
      <CalibrationModal
        isOpen={isCalibrationModalOpen}
        onClose={createCalibrationCloseHandler(fabricCanvas?.current, setCalibrationModalOpen)}
        onSubmit={createCalibrationSubmitHandler(fabricCanvas?.current, () => {
          const newCalibrationData = getCalibrationFromLocalStorage();
          setLocalCalibrationData(newCalibrationData ?? fabricConfigs.calibrationData!);
          setCalibrationModalOpen(false);
          changeToolMode("measure");
        })}
      />
      <CalibrationPrompt
        isOpen={isCalibrationPromptOpen}
        onClose={() => setCalibrationPromptOpen(false)}
        onCalibrate={() => {
          setCalibrationPromptOpen(false);
          changeToolMode("calibrate");
        }}
      />
    </>
  );
};

export default FabricToolbar;
