import { Canvas } from "fabric";
import { UndoAction, FabricMeasurementLine } from "@/shared/types";

/*
Creates an undo function that applies the last UndoAction and updates the stack.
Handles add, add-measurement (line+text), modify, and bypasses crop.
*/
export const createUndoHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  undoStack: UndoAction[],
  setUndoStack: React.Dispatch<React.SetStateAction<UndoAction[]>>,
  initialObjectCount: React.MutableRefObject<number>,
  isUndoing: React.MutableRefObject<boolean>
) => {
  return async () => {
    if (!fabricCanvas?.current || !undoStack.length) return;

    const canvas = fabricCanvas.current;
    const action = undoStack[undoStack.length - 1];

    if (action.type === "crop") {
      setUndoStack((prev) => prev.slice(0, -1));
      return;
    }

    isUndoing.current = true;

    if (action.type === "add") {
      const objects = canvas.getObjects();
      if (objects.length > initialObjectCount.current) {
        canvas.remove(objects[objects.length - 1]);
        canvas.renderAll();
      }
    } else if (action.type === "add-measurement") {
      const measurementLines = canvas
        .getObjects()
        .filter((obj) => (obj as any).name === "measurementLine") as FabricMeasurementLine[];
      const lineObj = measurementLines[measurementLines.length - 1];

      if (lineObj) {
        canvas.remove(lineObj);
        if (lineObj.measurementText) {
          canvas.remove(lineObj.measurementText);
        }
      }
      canvas.renderAll();
    } else if (action.type === "modify") {
      const obj = canvas
        .getObjects()
        .find((o) => (o as any as { id: string }).id === action.objectId);
      if (obj && action.previousState) {
        obj.set(action.previousState);
        obj.setCoords();
        canvas.renderAll();
      }
    }
    setUndoStack((prev) => prev.slice(0, -1));
    isUndoing.current = false;
  };
};
