import React from "react";
import { FaPalette } from "react-icons/fa";

type ColorControlsProps = {
  color: string;
  onChange: (color: string) => void;
};

const ColorControls: React.FC<ColorControlsProps> = ({ color, onChange }) => {
  return (
    <div className="tool-grid">
      <label className="tool-btn color-picker-btn" title="Annotation Color">
        <FaPalette style={{ color: color }} />
        <input
          type="color"
          value={color}
          onChange={(e) => onChange(e.target.value)}
          aria-label="annotation-color-picker"
          className="color-input-hidden"
        />
      </label>
    </div>
  );
};

export default ColorControls;
