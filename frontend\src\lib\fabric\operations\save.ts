import { Canvas } from "fabric";
import { saveFabricConfig } from "@/shared/api";
import { CropData, FabricConfig, TransformState, CalibrationData } from "@/shared/types";
import { getCalibrationFromLocalStorage, clearCalibrationFromLocalStorage } from "./calibration";

/*
Creates a save function that serializes annotations, filters, crop, transforms,
and calibration data and persists them through the API.
*/
export const createSaveHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  dataId: string,
  filters: Record<string, any>,
  canvasCropData: CropData,
  transformState: TransformState,
  calibrationData?: CalibrationData
) => {
  return async () => {
    if (fabricCanvas?.current) {
      const annotations = fabricCanvas.current.toJSON();
      annotations.canvasWidth = fabricCanvas.current.getWidth();
      annotations.canvasHeight = fabricCanvas.current.getHeight();

      const finalCalibrationData = getCalibrationFromLocalStorage();
      const annotationColor = (fabricCanvas.current as any).annotationColor as string | undefined;

      const fabricConfig: FabricConfig = {
        brightness: filters.brightness,
        contrast: filters.contrast,
        grayscale: filters.grayscale,
        invert: filters.invert,
        sharpness: filters.sharpness,
        gammaR: filters.gammaR,
        gammaG: filters.gammaG,
        gammaB: filters.gammaB,
        ...(annotationColor && { annotationColor }),
        annotations,
        cropData: canvasCropData,
        transformState,
        ...(finalCalibrationData && { calibrationData: finalCalibrationData }),
      };

      await saveFabricConfig(dataId, fabricConfig);
      if (finalCalibrationData && !calibrationData) {
        clearCalibrationFromLocalStorage();
      }
    }
  };
};
