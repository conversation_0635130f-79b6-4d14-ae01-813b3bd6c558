import { ToolMode } from "@/shared/types";
import { Canvas } from "fabric";
const inheritedFont = window.getComputedStyle(document.body).fontFamily;

export const getToolConfig = (mode: ToolMode) => {
  switch (mode) {
    case "freehand":
      return {
        width: 2,
        strokeUniform: true,
      };
    case "text":
      return {
        fontSize: 20,
        fontFamily: inheritedFont,
        selectable: false,
        evented: false,
        strokeUniform: true,
      };
    case "rect":
      return {
        width: 1,
        height: 1,
        fill: "transparent",
        strokeWidth: 2,
        strokeUniform: true,
        selectable: false,
        evented: false,
      };
    case "highlight":
      return {
        width: 1,
        height: 1,
        fill: "rgba(255, 255, 0, 0.25)",
        stroke: "#e1c542",
        strokeWidth: 0,
        strokeUniform: true,
        selectable: false,
        evented: false,
        name: "highlight",
      };
    case "circle":
      return {
        radius: 1,
        fill: "transparent",
        strokeWidth: 2,
        selectable: false,
        evented: false,
        strokeUniform: true,
      };
    case "line":
      return {
        strokeWidth: 2,
        selectable: false,
        evented: false,
        strokeUniform: true,
        name: "line",
      };
    case "crop":
      return {
        width: 1,
        height: 1,
        fill: "rgba(255, 255, 255, .1)",
        stroke: "#ff0000",
        strokeWidth: 1,
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false,
        strokeUniform: true,
        name: "cropRect",
      };
    case "measure":
      return {
        strokeWidth: 2,
        originX: "center",
        originY: "center",
        selectable: false,
        evented: false,
        hasControls: false,
        hasBorders: false,
        lockScalingFlip: true,
        name: "measurementLine",
        strokeUniform: true,
      };
    case "calibrate":
      return {
        width: 4,
        height: 4,
        fill: "red",
        stroke: "white",
        strokeWidth: 1,
        selectable: false,
        evented: false,
        originX: "center",
        originY: "center",
        name: "calibrateLine",
      };
    case "arrow":
      return {
        strokeWidth: 2,
        selectable: false,
        evented: false,
        strokeUniform: true,
        name: "arrow",
        arrowHeadSize: 14,
      };
    default:
      return {};
  }
};

export const constrainToCanvas = (pointer: { x: number; y: number }, canvas: Canvas) => {
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  return {
    x: Math.max(0, Math.min(pointer.x, canvasWidth - 1)),
    y: Math.max(0, Math.min(pointer.y, canvasHeight - 1)),
  };
};

export const transformPointer = (
  pointer: { x: number; y: number },
  canvas: Canvas
): { x: number; y: number } => {
  const vpt = canvas.viewportTransform;
  const needsTransform = vpt && (vpt[0] !== 1 || vpt[3] !== 1 || vpt[4] !== 0 || vpt[5] !== 0);

  let transformedPointer = pointer;

  if (needsTransform) {
    transformedPointer = {
      x: (pointer.x - vpt[4]) / vpt[0],
      y: (pointer.y - vpt[5]) / vpt[3],
    };
  }

  return transformedPointer;
};
