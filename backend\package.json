{"name": "csoi-backend", "version": "1.0.0", "description": "Backend API server for CSOI medical viewer application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "path": "^0.12.7", "mongodb": "^6.3.0", "mongoose": "^8.0.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["medical", "dicom", "api", "express"], "author": "CSOI Team", "license": "MIT"}