import { Canvas } from "fabric";
import { useCallback, useEffect, useRef } from "react";
import { UseResponsiveCanvasProps, CropData } from "@/shared/types";
import {
  calculateFittedCanvasDimensions,
  shouldResize,
  scaleCanvasObjects,
} from "@/lib/fabric/rendering";
/*
Observes container size changes and resizes Fabric canvas while preserving
background scaling rules and annotation coordinates.
*/

const handleCroppedCanvasResize = (
  canvas: Canvas,
  container: HTMLElement,
  cropData: CropData,
  setCropData?: (data: CropData) => void
) => {
  if (!cropData?.normalizedCropRect) return;

  const containerRect = container.getBoundingClientRect();
  const currentCanvasWidth = canvas.getWidth();
  const currentCanvasHeight = canvas.getHeight();

  const cropAspectRatio = currentCanvasWidth / currentCanvasHeight;
  const containerAspectRatio = containerRect.width / containerRect.height;

  let targetWidth: number;
  let targetHeight: number;

  if (cropAspectRatio > containerAspectRatio) {
    targetWidth = containerRect.width;
    targetHeight = containerRect.width / cropAspectRatio;
  } else {
    targetHeight = containerRect.height;
    targetWidth = containerRect.height * cropAspectRatio;
  }

  if (!shouldResize(currentCanvasWidth, currentCanvasHeight, targetWidth, targetHeight)) {
    return;
  }

  const scaleX = targetWidth / currentCanvasWidth;
  const scaleY = targetHeight / currentCanvasHeight;

  canvas.setDimensions({ width: targetWidth, height: targetHeight });

  const backgroundImage = canvas.backgroundImage;
  if (backgroundImage) {
    backgroundImage.set({
      left: targetWidth / 2,
      top: targetHeight / 2,
      originX: "center",
      originY: "center",
      scaleX: (backgroundImage.scaleX || 1) * scaleX,
      scaleY: (backgroundImage.scaleY || 1) * scaleY,
    });
  }

  canvas.forEachObject((obj) => {
    if (obj === backgroundImage) return;

    obj.set({
      left: (obj.left || 0) * scaleX,
      top: (obj.top || 0) * scaleY,
      scaleX: (obj.scaleX || 1) * scaleX,
      scaleY: (obj.scaleY || 1) * scaleY,
    });
    obj.setCoords();
  });
  scaleCanvasObjects(canvas, 1);
  canvas.renderAll();

  if (setCropData) {
    const updatedCropData = {
      ...cropData,
      canvasDimensions: {
        width: containerRect.width,
        height: containerRect.height,
      },
    };
    setCropData(updatedCropData);
  }
};

const handleOriginalCanvasResize = (canvas: Canvas, container: HTMLElement) => {
  const containerRect = container.getBoundingClientRect();
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const { width: fittedWidth, height: fittedHeight } = calculateFittedCanvasDimensions(
    currentWidth,
    currentHeight,
    containerRect.width,
    containerRect.height
  );

  const { width: targetWidth, height: targetHeight } = {
    width: Math.max(fittedWidth, 300),
    height: Math.max(fittedHeight, 200),
  };
  if (!shouldResize(currentWidth, currentHeight, targetWidth, targetHeight)) {
    return;
  }

  const scaleX = targetWidth / currentWidth;
  const scaleY = targetHeight / currentHeight;
  const imageScale = Math.min(scaleX, scaleY);
  const actualWidth = currentWidth * imageScale;
  const actualHeight = currentHeight * imageScale;

  canvas.setDimensions({ width: actualWidth, height: actualHeight });
  scaleCanvasObjects(canvas, imageScale);

  const backgroundImage = canvas.backgroundImage;
  if (backgroundImage) {
    const currentAngle = backgroundImage.angle || 0;
    const currentFlipX = backgroundImage.flipX || false;
    const currentFlipY = backgroundImage.flipY || false;

    backgroundImage.scaleX = (backgroundImage.scaleX || 1) * imageScale;
    backgroundImage.scaleY = (backgroundImage.scaleY || 1) * imageScale;

    backgroundImage.set({
      left: actualWidth / 2,
      top: actualHeight / 2,
      originX: "center",
      originY: "center",
    });

    backgroundImage.angle = currentAngle;
    backgroundImage.flipX = currentFlipX;
    backgroundImage.flipY = currentFlipY;
  }
  canvas.requestRenderAll();
};

export const useResponsiveCanvas = ({
  fabricCanvas,
  containerRef,
  cropData,
  setCropData,
}: UseResponsiveCanvasProps) => {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const resizeCanvas = useCallback(() => {
    if (!fabricCanvas?.current || !containerRef.current) return;

    const canvas = fabricCanvas.current;

    if (cropData?.isCropped && cropData.normalizedCropRect && cropData.canvasDimensions) {
      handleCroppedCanvasResize(canvas, containerRef.current, cropData, setCropData);
    } else {
      handleOriginalCanvasResize(canvas, containerRef.current);
    }
  }, [fabricCanvas, containerRef, cropData, setCropData]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    resizeObserverRef.current = new ResizeObserver(() => {
      resizeCanvas();
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;
    };
  }, [containerRef, resizeCanvas]);

  return {
    resizeCanvas,
  };
};
