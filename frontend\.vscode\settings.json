{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "eslint.enable": true, "eslint.useFlatConfig": true, "eslint.validate": ["typescript", "typescriptreact"], "prettier.requireConfig": false, "search.exclude": {"**/dist": true, "**/node_modules": true}, "typescript.tsdk": "node_modules/typescript/lib"}