import { useRef, useState } from "react";
import { Canvas } from "fabric";
import { UndoAction, UndoTrackingState } from "@/shared/types";
import { createUndoHandler } from "@/lib/fabric/operations";

export const useUndoTracking = (
  /*
Initializes undo stack and helpers, providing addUndoAction and an isUndoing
flag to coordinate with event handlers and drawing updates.
*/

  fabricCanvas: React.RefObject<Canvas | null>,
  initialObjectCount: React.MutableRefObject<number>
): UndoTrackingState => {
  const [undoStack, setUndoStack] = useState<UndoAction[]>([]);
  const isUndoingRef = useRef<boolean>(false);

  const handleUndo = createUndoHandler(
    fabricCanvas,
    undoStack,
    setUndoStack,
    initialObjectCount,
    isUndoingRef
  );

  const disableUndoTracking = () => {
    isUndoingRef.current = true;
  };
  const enableUndoTracking = () => {
    isUndoingRef.current = false;
  };

  return {
    undoStack,
    canUndo: undoStack.length > 0,
    isUndoingRef,
    handleUndo,
    addUndoAction: (action: UndoAction) => {
      if (isUndoingRef.current) return;
      setUndoStack((prev) => [...prev, action]);
    },
    disableUndoTracking,
    enableUndoTracking,
  };
};
