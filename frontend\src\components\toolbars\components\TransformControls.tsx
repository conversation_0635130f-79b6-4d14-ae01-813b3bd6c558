import React from "react";
import { FaRedo, FaArrowsAltH, FaArrowsAltV, FaFilter, FaExchangeAlt } from "react-icons/fa";
import type { TransformControlsProps } from "@/shared/types";

const TransformControls: React.FC<TransformControlsProps> = ({
  grayscale,
  invert,
  disableGrayscale,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onGrayscaleChange,
  onInvertChange,
}) => {
  return (
    <>
      <div className="tool-grid">
        <button className="tool-btn" onClick={onRotate} title="Rotate selected object">
          <FaRedo />
        </button>
        <button className="tool-btn" onClick={onFlipHorizontal} title="Flip horizontal">
          <FaArrowsAltH />
        </button>
        <button className="tool-btn" onClick={onFlipVertical} title="Flip vertical">
          <FaArrowsAltV />
        </button>
        <button
          className={`tool-btn ${grayscale ? "active" : ""} ${disableGrayscale ? "disabled" : ""}`}
          onClick={() => !disableGrayscale && onGrayscaleChange(!grayscale)}
          disabled={disableGrayscale}
          title="Toggle grayscale"
        >
          <FaFilter />
        </button>
        <button
          className={`tool-btn ${invert ? "active" : ""}`}
          onClick={() => onInvertChange(!invert)}
          title="Toggle invert"
        >
          <FaExchangeAlt />
        </button>
      </div>
    </>
  );
};

export default TransformControls;
