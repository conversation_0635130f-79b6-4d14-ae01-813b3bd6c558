import { useState } from "react";
import { Canvas } from "fabric";
import { TransformState, ImageTransformsState } from "@/shared/types";
import {
  createRotateHand<PERSON>,
  createFlipHorizontalHandler,
  createFlipVerticalHandler,
} from "@/lib/fabric/operations";
/*
Controls rotation and flips for the image/canvas. Tracks transform state locally
and wires to operations handlers.
*/

export const useImageTransforms = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialTransformState: TransformState,
  onRotationComplete?: () => void
): ImageTransformsState => {
  const [transformState, setTransformState] = useState<TransformState>(initialTransformState);

  const handleRotate = createRotateHandler(fabricCanvas, setTransformState, onRotationComplete);
  const handleFlipHorizontal = createFlipHorizontalHandler(fabricCanvas, setTransformState);
  const handleFlipVertical = createFlipVerticalHandler(fabricCanvas, setTransformState);

  return {
    transformState,
    setTransformState,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
  };
};
