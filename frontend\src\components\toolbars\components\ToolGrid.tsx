import React from "react";
import { ToolDefinition, ToolGridProps } from "@/shared/types";
import { toolDefinitions } from "@/lib/fabric/tools";

const ToolGrid: React.FC<ToolGridProps> = ({
  activeMode,
  hasPerformedCrop,
  onToolSelect,
  onCrop,
}) => {
  const handleToolClick = async (tool: ToolDefinition) => {
    if (tool.mode === "crop" && hasPerformedCrop) {
      if (onCrop) {
        await onCrop();
        onToolSelect("crop");
      }
      return;
    }

    onToolSelect(tool.mode);
  };

  return (
    <div className="tool-grid">
      {toolDefinitions.map((tool) => {
        const IconComponent = tool.icon;
        const isCropInactive = tool.mode === "crop" && hasPerformedCrop;

        return (
          <button
            key={tool.mode}
            className={`tool-btn ${activeMode === tool.mode ? "active" : ""} ${
              isCropInactive ? "inactive" : ""
            }`}
            onClick={() => handleToolClick(tool)}
            title={tool.mode === "crop" && hasPerformedCrop ? "Restore original image" : tool.title}
          >
            <IconComponent />
          </button>
        );
      })}
    </div>
  );
};

export default ToolGrid;
