import { Dialog, Button } from "@blueprintjs/core";

interface CalibrationPromptProps {
  isOpen: boolean;
  onClose: () => void;
  onCalibrate: () => void;
}

const CalibrationPrompt = ({ isOpen, onClose, onCalibrate }: CalibrationPromptProps) => {
  const handleCalibrate = () => {
    onCalibrate();
    onClose();
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title="Calibration Required"
      style={{
        width: "320px",
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
      }}
      canEscapeKeyClose={true}
      canOutsideClickClose={true}
    >
      <div style={{ padding: "10px 20px", textAlign: "center" }}>
        <p style={{ marginBottom: "16px", fontSize: "14px" }}>
          Calibration is required to use the measurement tool.
        </p>
        <p style={{ marginBottom: "20px", fontSize: "14px", fontWeight: "bold" }}>
          Please calibrate first to enable accurate measurements in mm.
        </p>
        <div style={{ display: "flex", justifyContent: "center", gap: "12px" }}>
          <Button onClick={handleCalibrate} intent="primary">
            Calibrate Now
          </Button>
          <Button onClick={onClose}>Cancel</Button>
        </div>
      </div>
    </Dialog>
  );
};

export default CalibrationPrompt;
