import { useEffect, useState } from "react";
import { PencilBrush, FabricObject } from "fabric";
import { ToolMode, FabricMeasurementLine, UseFabricToolsProps } from "@/shared/types";
import {
  setToolMode,
  getToolConfig,
  startDrawingShape,
  updateDrawingShape,
  finishDrawingShape,
  transformPointer,
} from "@/lib/fabric/tools";
import { setupCanvasEventListeners } from "@/lib/fabric/events/canvasEventListeners";
import { UndoTrackingState, FabricObjectState } from "@/shared/types";
import { createMeasurementCheckHandler } from "@/lib/fabric/operations";

/*
Handles tool mode, mouse drawing interactions, and calibration gating for measure.
Connects Fabric events to drawingInteractions utilities.
*/
export const useFabricTools = ({
  fabricCanvas,
  hasPerformedCrop = false,
  onShapeCreated,
  onCrop,
  disableUndoTracking,
  enableUndoTracking,
  showCalibrationModal,
  calibrationData,
  onCalibrationPrompt,
}: UseFabricToolsProps) => {
  const [activeMode, setActiveMode] = useState<ToolMode | null>(null);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [currentShape, setCurrentShape] = useState<FabricObject | FabricMeasurementLine | null>(
    null
  );

  const [calibrationPoints, setCalibrationPoints] = useState<{ x: number; y: number }[]>([]);

  // Switches active tool; for 'measure', requires calibration before enabling
  const changeToolMode = (mode: ToolMode) => {
    if (!fabricCanvas?.current) return;

    if (mode === "measure") {
      const checkCalibration = createMeasurementCheckHandler(calibrationData, onCalibrationPrompt);
      if (!checkCalibration()) {
        return;
      }
    }

    setActiveMode(mode);
    setToolMode(mode, fabricCanvas.current);
  };

  useEffect(() => {
    if (!fabricCanvas?.current) return;

    const canvas = fabricCanvas.current;

    // Configure freehand brush from tool config
    const config = getToolConfig("freehand") as any;
    canvas.freeDrawingBrush = new PencilBrush(canvas);
    const brushColor = (canvas as any).annotationColor || config.color;
    canvas.freeDrawingBrush.color = brushColor;
    canvas.freeDrawingBrush.width = config.width;

    if (activeMode) {
      setToolMode(activeMode, canvas);
    }

    // Reuse undo/object-state structures if the canvas already has them
    const undoTracking: UndoTrackingState = (canvas as any).undoTrackingState || {
      isUndoingRef: { current: false },
      addUndoAction: () => {},
    };
    const objectStates: React.MutableRefObject<Map<string, FabricObjectState>> = (canvas as any)
      .objectStates || { current: new Map() };

    // Wire Fabric events to undo tracking and measurement/arrow modifiers
    const disposers = setupCanvasEventListeners(canvas, undoTracking, objectStates, onShapeCreated);

    const mouseDownHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!activeMode || !e.pointer) return;

      if (activeMode === "calibrate") {
        const point = transformPointer(e.pointer, canvas);
        const updatedPoints = [...calibrationPoints, point];
        setCalibrationPoints(updatedPoints);
      }

      const result = startDrawingShape(
        e.pointer,
        activeMode,
        canvas,
        hasPerformedCrop,
        onShapeCreated
      );

      if (result) {
        setStartPoint(result.startPoint);
        setCurrentShape(result.shape);
      }
    };

    const mouseMoveHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!currentShape || !startPoint || !e.pointer || !activeMode) return;

      // Update geometry during drag; measurement lines refresh overlay text
      updateDrawingShape(
        e.pointer,
        activeMode,
        canvas,
        currentShape,
        startPoint,
        disableUndoTracking,
        enableUndoTracking,
        calibrationData
      );
    };

    const mouseUpHandler = () => {
      if (!currentShape || !activeMode) {
        setStartPoint(null);
        setCurrentShape(null);
        return;
      }

      // Finalize interaction; crop toggles and measurement text stabilization
      finishDrawingShape(
        activeMode,
        currentShape,
        canvas,
        onCrop,
        onShapeCreated,
        disableUndoTracking,
        enableUndoTracking,
        calibrationData
      );
      if (activeMode === "calibrate") {
        if (calibrationPoints.length === 2) {
          setCalibrationPoints([]);
          showCalibrationModal?.();
        }
      }
      setStartPoint(null);
      setCurrentShape(null);
    };

    const disposeMouseDown = canvas.on("mouse:down", mouseDownHandler);
    const disposeMouseMove = canvas.on("mouse:move", mouseMoveHandler);
    const disposeMouseUp = canvas.on("mouse:up", mouseUpHandler);

    return () => {
      disposeMouseDown();
      disposeMouseMove();
      disposeMouseUp();
      disposers.forEach((dispose) => dispose && dispose());
    };
  }, [
    fabricCanvas,
    activeMode,
    startPoint,
    currentShape,
    onShapeCreated,
    hasPerformedCrop,
    onCrop,
    disableUndoTracking,
    enableUndoTracking,
    calibrationPoints,
    calibrationData,
    showCalibrationModal,
  ]);

  return {
    activeMode,
    changeToolMode,
  };
};
