import { <PERSON>vas, FabricText } from "fabric";
import { FabricMeasurementLine, CalibrationData } from "@/shared/types";
import { getCalibrationFromLocalStorage } from "./calibration";

/*
Converts a pixel length to mm using calibration data and current background scale.
*/
const getCalibratedDistanceInMM = (
  pixelLength: number,
  canvas: Canvas,
  calibrationData: CalibrationData
): number => {
  const { calibrationDistance, calibratedPixelLength, calibrationImageScale } = calibrationData;
  const currentScale = canvas.backgroundImage?.scaleX ?? 1;

  const scaleRatio = currentScale / calibrationImageScale;
  const correctedPx = pixelLength / scaleRatio;

  return (calibrationDistance / calibratedPixelLength) * correctedPx;
};

export const isCalibrated = (calibrationData?: CalibrationData): boolean => {
  return !!(calibrationData && calibrationData.calibrationDistance > 0);
};

/*
Returns a function that checks calibration before measuring and optionally opens
calibration UI if missing.
*/
export const createMeasurementCheckHandler = (
  calibrationData?: CalibrationData,
  onCalibrationPrompt?: () => void
) => {
  return (): boolean => {
    const localStorageData = getCalibrationFromLocalStorage();
    const finalCalibrationData =
      localStorageData && isCalibrated(localStorageData) ? localStorageData : calibrationData;

    if (!isCalibrated(finalCalibrationData)) {
      onCalibrationPrompt?.();
      return false;
    }
    return true;
  };
};

/*
Creates a centered overlay text showing distance in mm for a measurement line.
*/
export const createMeasurementText = (
  line: FabricMeasurementLine,
  canvas: Canvas,
  calibrationData: CalibrationData
): FabricText => {
  const x1 = line.left! + line.x1! * line.scaleX!;
  const y1 = line.top! + line.y1! * line.scaleY!;
  const x2 = line.left! + line.x2! * line.scaleX!;
  const y2 = line.top! + line.y2! * line.scaleY!;

  const realDistance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);

  const distanceMM = getCalibratedDistanceInMM(realDistance, canvas, calibrationData);
  const inheritedFont = window.getComputedStyle(document.body).fontFamily;
  const text = new FabricText(`${distanceMM.toFixed(2)} mm`, {
    fontSize: 20,
    fill: (line.stroke as string) || ((canvas as any).annotationColor as string) || "#ff0000",
    fontFamily: inheritedFont,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    selectable: false,
    evented: false,
    hoverCursor: "default",
    moveCursor: "default",
    hasControls: false,
    hasBorders: false,
    lockMovementX: true,
    lockMovementY: true,
    name: "measurementText",
    originX: "center",
    originY: "center",
    id: `${(line as any).id}-text`,
    parentId: (line as any).id,
    strokeUniform: true,
    scaleX: 1,
    scaleY: 1,
  });

  return text;
};

/*
Ensures measurement text exists and updates its content and position.
*/
export const updateMeasurementText = (
  canvas: Canvas,
  line: FabricMeasurementLine,
  calibrationData: CalibrationData
): void => {
  if (!line.measurementText) {
    line.measurementText = createMeasurementText(line, canvas, calibrationData);
    canvas.add(line.measurementText);
  }

  const lineCenter = line.getCenterPoint();
  const x1 = line.left! + line.x1! * line.scaleX!;
  const y1 = line.top! + line.y1! * line.scaleY!;
  const x2 = line.left! + line.x2! * line.scaleX!;
  const y2 = line.top! + line.y2! * line.scaleY!;

  const realDistance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);

  const distanceMM = getCalibratedDistanceInMM(realDistance, canvas, calibrationData);

  line.measurementText.set({
    text: `${distanceMM.toFixed(2)} mm`,
    left: lineCenter.x,
    top: lineCenter.y,
  });

  canvas.renderAll();
};

/*
Keeps measurement text centered on its line while the line is modified.
*/
export const updateMeasurementOnModify = (canvas: Canvas, line: FabricMeasurementLine): void => {
  if (!line.measurementText) return;

  const lineCenter = line.getCenterPoint();
  line.measurementText.set({
    left: lineCenter.x,
    top: lineCenter.y,
  });

  canvas.renderAll();
};

export const isMeasurementLine = (obj: any): obj is FabricMeasurementLine => {
  return (obj as any)?.name === "measurementLine" && (obj as any)?.type === "line";
};
