{"name": "csoi-web", "private": true, "version": "1.0.0", "type": "module", "description": "Frontend for CSOI Web Application", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@blueprintjs/core": "^6.1.0", "@blueprintjs/icons": "^6.0.0", "@cornerstonejs/core": "^3.19.1", "@cornerstonejs/dicom-image-loader": "^3.19.1", "@cornerstonejs/tools": "^3.19.1", "@kitware/vtk.js": "^32.12.1", "axios": "^1.9.0", "dicom-parser": "^1.8.21", "fabric": "^6.6.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-rnd": "^10.5.2", "react-router-dom": "^7.6.1", "uuid": "^11.1.0"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@types/fabric": "^5.3.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.11.0", "sass": "^1.86.0", "typescript": "^5.8.2", "typescript-eslint": "^8.11.0", "vite": "^6.2.2"}, "keywords": ["medical-imaging", "dicom", "cornerstone", "fabric", "react", "typescript", "vite"], "author": "CSOI Team", "license": "MIT"}