import axios from "axios";
import type {
  ApiResponse,
  ImageData,
  VolumeData,
  VolumeConfig,
  FabricConfig,
  PatientDetails,
} from "@/shared/types";

const baseUrl = import.meta.env.VITE_DICOM_BASE_URL;

const api = axios.create({
  baseURL: baseUrl,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});
const handleApiGet = async <T>(url: string, errorMessage: string): Promise<T | null> => {
  try {
    const response = await api.get<ApiResponse<T>>(url);
    return response.data.data;
  } catch (error) {
    console.error(errorMessage, error);
    return null;
  }
};

export async function apiGetStackData(id: string): Promise<ImageData | null> {
  return handleApiGet<ImageData>(`/api/dicom/stack/${id}`, "Error fetching stack data:");
}

export async function apiGetVolumeData(id: string): Promise<VolumeData | null> {
  return handleApiGet<VolumeData>(`/api/dicom/volume/${id}`, "Error fetching volume data:");
}

export async function apiGetImageData(id: string): Promise<ImageData | null> {
  return handleApiGet<ImageData>(`/api/image/${id}`, "Error fetching image data:");
}

export async function apiGetFileList(patientId: string): Promise<PatientDetails | null> {
  return handleApiGet<PatientDetails>(
    `/api/patient/${patientId}/files`,
    "Error fetching patient files:"
  );
}

const handleApiSave = async <T>(
  url: string,
  configs: T,
  successMessage: string,
  errorMessage: string
): Promise<void> => {
  try {
    await api.patch<ApiResponse<T>>(url, configs);
    console.log(successMessage);
  } catch (error) {
    console.error(errorMessage, error);
  }
};

export async function saveVolumeConfig(id: string, configs: VolumeConfig): Promise<void> {
  return handleApiSave(
    `/api/dicom/volume/${id}/config`,
    configs,
    "Volume settings saved successfully",
    "Error saving volume config:"
  );
}

export async function saveFabricConfig(
  id: string,
  configs: FabricConfig,
  type: "stack" | "image" = "stack"
): Promise<void> {
  const endpoint =
    type === "image" ? `/api/image/${id}/fabric-config` : `/api/dicom/stack/${id}/fabric-config`;
  return handleApiSave(
    endpoint,
    configs,
    "Fabric settings saved successfully",
    "Error saving fabric config:"
  );
}
