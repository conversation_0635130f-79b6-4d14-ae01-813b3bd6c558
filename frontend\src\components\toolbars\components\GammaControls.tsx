import React from "react";
import { FaUndo, FaCircle } from "react-icons/fa";
import type { GammaControlsProps } from "@/shared/types";

const GammaControls: React.FC<GammaControlsProps> = ({
  gammaR,
  gammaG,
  gammaB,
  disableGamma,
  onGammaRChange,
  onGammaGChange,
  onGammaBChange,
}) => {
  const gammaSliders = [
    {
      icon: FaCircle,
      label: "Gamma R",
      value: gammaR,
      onChange: onGammaRChange,
      className: "gamma-red",
      min: 1,
      max: 2.2,
      step: 0.05,
      default: 1,
    },
    {
      icon: FaCircle,
      label: "Gamma G",
      value: gammaG,
      onChange: onGammaGChange,
      className: "gamma-green",
      min: 1,
      max: 2.2,
      step: 0.05,
      default: 1,
    },
    {
      icon: FaCircle,
      label: "Gamma B",
      value: gammaB,
      onChange: onGammaBChange,
      className: "gamma-blue",
      min: 1,
      max: 2.2,
      step: 0.05,
      default: 1,
    },
  ];

  return (
    <div className={`gamma-controls ${disableGam<PERSON> ? "disabled" : ""}`}>
      {gammaSliders.map((slider) => {
        const IconComponent = slider.icon;
        return (
          <div key={slider.label} className="control-item">
            <div className={`control-icon ${slider.className}`} title={slider.label}>
              <IconComponent />
            </div>
            <input
              type="range"
              min={slider.min}
              max={slider.max}
              step={slider.step}
              value={slider.value}
              onChange={(e) => !disableGamma && slider.onChange(parseFloat(e.target.value))}
              className={`horizontal-slider ${slider.className}`}
              disabled={disableGamma}
              title={`${slider.label}: ${(slider.value || 1).toFixed(2)}`}
            />
            <span>{(slider.value || 1).toFixed(2)}</span>
            <button
              type="button"
              className="reset-slider-btn"
              title={`Reset ${slider.label}`}
              onClick={() => !disableGamma && slider.onChange(slider.default)}
              disabled={disableGamma}
            >
              <FaUndo className="control-icon" />
            </button>
          </div>
        );
      })}
    </div>
  );
};

export default GammaControls;
